#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Simplified Translation Workflow
===================================

Test the new simplified 3-step approach:
1. Mask LaTeX blocks with placeholders
2. Translate only English text  
3. Restore original LaTeX blocks
"""

import sys
import os
import pandas as pd

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simplified_workflow():
    """Test the simplified workflow"""
    print("🧪 Testing Simplified Translation Workflow")
    print("=" * 60)
    
    # Import the pipeline
    try:
        from math_translation_pipeline import MathTranslationPipeline
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide the window
        pipeline = MathTranslationPipeline(root)
    except Exception as e:
        print(f"❌ Error importing pipeline: {e}")
        return False
    
    # Test cases
    test_cases = [
        {
            'input': r"\text{Find the limit of each of the sequences } (a_n) \text{ in the following cases:} a_n = \frac{1}{2^n}",
            'expected_pattern': r"limit.*barisan.*kasus.*a_n.*frac"
        },
        {
            'input': r"a_n = \frac{3^n - 1}{3^n}",
            'expected_pattern': r"a_n.*frac.*3\^n"
        },
        {
            'input': r"\text{Taking Limit} \lim_{n \to \infty} a_n = \lim_{n \to \infty} \frac{1}{2^n}",
            'expected_pattern': r"lim.*infty.*frac"
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases):
        print(f"\n📝 Test Case {i+1}:")
        print(f"Input: {test_case['input']}")
        
        try:
            # Step 1: Test masking
            masked_text, mapping = pipeline.simple_mask_latex_blocks(test_case['input'])
            print(f"Step 1 (Masked): {masked_text}")
            print(f"Mapping count: {len(mapping)}")
            
            # Step 2: Test translation (simplified)
            translated_text = masked_text.replace('Find the limit', 'Tentukan limit')
            translated_text = translated_text.replace('in the following cases', 'dalam kasus berikut')
            translated_text = translated_text.replace('Taking Limit', 'Mengambil Limit')
            print(f"Step 2 (Translated): {translated_text}")
            
            # Step 3: Test restoration
            restored_text = pipeline.restore_mathematical_expressions(translated_text, mapping)
            print(f"Step 3 (Restored): {restored_text}")
            
            # Check if mathematical expressions are preserved
            math_preserved = True
            for token, info in mapping.items():
                original_expr = info['original']
                if original_expr not in restored_text:
                    print(f"   ⚠️ Missing: {original_expr}")
                    math_preserved = False
            
            if math_preserved:
                print("   ✅ All mathematical expressions preserved")
                success_count += 1
            else:
                print("   ❌ Some mathematical expressions missing")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n🎯 Results: {success_count}/{len(test_cases)} tests passed")
    
    if success_count == len(test_cases):
        print("🎉 ALL TESTS PASSED!")
        print("✅ Simplified workflow is working correctly")
        return True
    else:
        print("⚠️ Some tests failed")
        return False

def test_end_to_end():
    """Test end-to-end with a small dataset"""
    print("\n🔄 Testing End-to-End Workflow")
    print("=" * 60)
    
    # Create a small test dataset
    test_data = {
        'Question': [
            r"\text{Find the limit of each of the sequences } (a_n) \text{ in the following cases:} a_n = \frac{1}{2^n}",
            r"a_n = \frac{3^n - 1}{3^n}",
            r"\text{Show that the sequence is divergent} a_n = 2^n"
        ],
        'Answer_key': [
            r"\lim_{n \to \infty} a_n = 0",
            r"1",
            r"\{a_n\} \text{ is divergent}"
        ]
    }
    
    df = pd.DataFrame(test_data)
    print(f"Test dataset: {len(df)} rows")
    
    try:
        from math_translation_pipeline import MathTranslationPipeline
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide the window
        pipeline = MathTranslationPipeline(root)
        
        # Test masking phase
        print("\n📝 Testing Masking Phase...")
        masked_questions = []
        mappings = []
        
        for question in df['Question']:
            masked, mapping = pipeline.simple_mask_latex_blocks(question)
            masked_questions.append(masked)
            mappings.append(mapping)
            print(f"   Masked: {masked}")
            print(f"   Tokens: {len(mapping)}")
        
        # Test restoration phase
        print("\n🔄 Testing Restoration Phase...")
        restored_questions = []
        
        for i, masked_q in enumerate(masked_questions):
            # Simulate translation (simple replacement)
            translated = masked_q.replace('Find the limit', 'Tentukan limit')
            translated = translated.replace('Show that the sequence', 'Tunjukkan bahwa barisan')
            translated = translated.replace('is divergent', 'adalah divergen')
            
            # Restore
            restored = pipeline.restore_mathematical_expressions(translated, mappings[i])
            restored_questions.append(restored)
            print(f"   Restored: {restored}")
        
        print("✅ End-to-end test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ End-to-end test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Simplified Translation Workflow Tests")
    print("=" * 70)
    
    all_tests_passed = True
    
    # Test 1: Basic workflow
    if not test_simplified_workflow():
        all_tests_passed = False
    
    # Test 2: End-to-end
    if not test_end_to_end():
        all_tests_passed = False
    
    print("\n" + "=" * 70)
    
    if all_tests_passed:
        print("🎉 ALL SIMPLIFIED WORKFLOW TESTS PASSED!")
        print("✅ The pipeline now uses a clean 3-step approach:")
        print("   1️⃣ Mask LaTeX blocks with placeholders")
        print("   2️⃣ Translate only English text")
        print("   3️⃣ Restore original LaTeX blocks")
        print("✅ This should be much more reliable and produce clean output!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("⚠️ Additional debugging may be needed.")
    
    return all_tests_passed

if __name__ == "__main__":
    main()
