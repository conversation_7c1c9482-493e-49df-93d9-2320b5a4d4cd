#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Translation Test
======================
"""

import re

def simple_workflow_test():
    """Test the 3-step workflow"""
    print("Testing Simplified Translation Workflow")
    print("=" * 50)
    
    # Your example
    text = r"\text{Find the limit of each of the sequences } (a_n) \text{ in the following cases:} a_n = \frac{1}{2^n}"
    print(f"Input: {text}")
    
    # Step 1: Mask LaTeX blocks
    mapping = {}
    counter = 1
    masked_text = text
    
    patterns = [
        r'\\text\{[^}]*\}',
        r'\\frac\{[^}]*\}\{[^}]*\}',
        r'[a-zA-Z]_[a-zA-Z0-9]+',
    ]
    
    for pattern in patterns:
        matches = list(re.finditer(pattern, masked_text))
        for match in reversed(matches):
            math_expr = match.group()
            if not (math_expr.startswith('[MATH') and math_expr.endswith(']')):
                placeholder = f'[MATH{counter}]'
                mapping[placeholder] = math_expr
                masked_text = masked_text[:match.start()] + placeholder + masked_text[match.end():]
                counter += 1
    
    print(f"Step 1 (Masked): {masked_text}")
    print(f"Mapping: {mapping}")
    
    # Step 2: Translate (simple replacement)
    translated = masked_text.replace('Find the limit of each of the sequences', 'Tentukan limit dari setiap barisan')
    translated = translated.replace('in the following cases', 'dalam kasus berikut')
    print(f"Step 2 (Translated): {translated}")
    
    # Step 3: Restore
    final = translated
    for placeholder, original in mapping.items():
        final = final.replace(placeholder, original)
    
    print(f"Step 3 (Final): {final}")
    
    print("\nExpected:")
    print(r"\text{Tentukan limit dari setiap barisan } (a_n) \text{ dalam kasus berikut:} a_n = \frac{1}{2^n}")
    
    return final

if __name__ == "__main__":
    simple_workflow_test()
