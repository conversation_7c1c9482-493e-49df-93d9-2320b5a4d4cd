#!/usr/bin/env python3
"""
Simplified Mathematical Expression Translation
=============================================

A clean, simple 3-step workflow:
1. Mask LaTeX blocks with placeholders
2. Translate only English text
3. Restore original LaTeX blocks
"""

import re
import pandas as pd

class SimplifiedTranslator:
    def __init__(self):
        self.math_mapping = {}
        self.counter = 1
    
    def step1_mask_latex_blocks(self, text):
        """Step 1: Replace all math expressions with placeholders"""
        if not isinstance(text, str) or pd.isna(text):
            return text
        
        masked_text = text
        self.math_mapping = {}
        self.counter = 1
        
        # Define LaTeX patterns to mask (in order of priority)
        patterns = [
            # Complete LaTeX expressions
            r'\\text\{[^}]*\}',                    # \text{content}
            r'\\frac\{[^}]*\}\{[^}]*\}',          # \frac{a}{b}
            r'\\sqrt\{[^}]*\}',                   # \sqrt{x}
            r'\\lim_{[^}]*}',                     # \lim_{n \to \infty}
            r'\\int_{[^}]*}\^{[^}]*}',           # \int_{a}^{b}
            r'\\sum_{[^}]*}\^{[^}]*}',           # \sum_{i=1}^{n}
            r'\\prod_{[^}]*}\^{[^}]*}',          # \prod_{i=1}^{n}
            
            # Simple LaTeX commands
            r'\\[a-zA-Z]+\{[^}]*\}',             # \command{content}
            r'\\[a-zA-Z]+',                      # \command
            
            # Mathematical expressions with subscripts/superscripts
            r'[a-zA-Z][a-zA-Z0-9]*_{[^}]+}',     # a_{n+1}
            r'[a-zA-Z][a-zA-Z0-9]*\^{[^}]+}',    # x^{2}
            r'[a-zA-Z0-9]+\^[a-zA-Z0-9]+',       # 2^n
            r'[a-zA-Z]_[a-zA-Z0-9]+',            # a_n
            
            # Mathematical symbols and operators
            r'[∫∑∏∆∇∂∞±≤≥≠≈∈∉⊂⊃∪∩∧∨¬∀∃ℝℕℤℚℂ]',
            r'[αβγδεζηθικλμνξοπρστυφχψω]',
            r'[ΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ]',
            
            # Fractions and special notation
            r'[0-9]+⁄[0-9]+',                    # Unicode fractions
            r'√[a-zA-Z0-9]+',                    # Unicode square root
            r'[a-zA-Z0-9]+[²³⁴⁵⁶⁷⁸⁹⁰¹]+',      # Unicode superscripts
            r'[a-zA-Z0-9]+[₀₁₂₃₄₅₆₇₈₉]+',       # Unicode subscripts
        ]
        
        # Apply patterns and replace with placeholders
        for pattern in patterns:
            matches = list(re.finditer(pattern, masked_text))
            for match in reversed(matches):  # Process from right to left
                math_expr = match.group()
                
                # Skip if already a placeholder
                if math_expr.startswith('[MATH') and math_expr.endswith(']'):
                    continue
                
                # Create placeholder
                placeholder = f'[MATH{self.counter}]'
                self.math_mapping[placeholder] = math_expr
                
                # Replace in text
                masked_text = masked_text[:match.start()] + placeholder + masked_text[match.end():]
                self.counter += 1
        
        return masked_text
    
    def step2_translate_english_text(self, masked_text, translation_service='google'):
        """Step 2: Translate only the English text, preserving placeholders"""
        if not isinstance(masked_text, str) or pd.isna(masked_text):
            return masked_text
        
        # For demonstration, we'll use simple replacements
        # In practice, this would call your translation service
        
        # Simple English to Indonesian translations for common mathematical terms
        translations = {
            'Find the limit of each of the sequences': 'Tentukan limit dari setiap barisan',
            'in the following cases': 'dalam kasus berikut',
            'Taking Limit': 'Mengambil Limit',
            'Applying Limit': 'Menerapkan Limit',
            'Show that the sequence': 'Tunjukkan bahwa barisan',
            'is defined by': 'didefinisikan oleh',
            'is divergent': 'adalah divergen',
            'is convergent': 'adalah konvergen',
            'Taking': 'Mengambil',
            'Applying': 'Menerapkan',
            'Given': 'Diberikan',
            'Let': 'Misalkan',
            'By Formula': 'Dengan Rumus',
            'This is Geometric Series': 'Ini adalah Deret Geometri',
            'Discuss the convergence or divergence of the following series': 'Diskusikan konvergensi atau divergensi dari deret berikut',
        }
        
        translated_text = masked_text
        
        # Apply translations while preserving placeholders
        for english, indonesian in translations.items():
            translated_text = translated_text.replace(english, indonesian)
        
        return translated_text
    
    def step3_restore_latex_blocks(self, translated_text):
        """Step 3: Replace placeholders with original LaTeX expressions"""
        if not isinstance(translated_text, str) or pd.isna(translated_text):
            return translated_text
        
        restored_text = translated_text
        
        # Replace placeholders with original math expressions
        for placeholder, original_expr in self.math_mapping.items():
            restored_text = restored_text.replace(placeholder, original_expr)
        
        return restored_text
    
    def translate_complete(self, text):
        """Complete 3-step translation workflow"""
        print(f"Input: {text}")
        
        # Step 1: Mask LaTeX blocks
        masked = self.step1_mask_latex_blocks(text)
        print(f"Step 1 (Masked): {masked}")
        print(f"Mapping: {self.math_mapping}")
        
        # Step 2: Translate English text
        translated = self.step2_translate_english_text(masked)
        print(f"Step 2 (Translated): {translated}")
        
        # Step 3: Restore LaTeX blocks
        final = self.step3_restore_latex_blocks(translated)
        print(f"Step 3 (Final): {final}")
        
        return final

def test_simplified_workflow():
    """Test the simplified workflow with your example"""
    print("🧪 Testing Simplified Translation Workflow")
    print("=" * 60)
    
    translator = SimplifiedTranslator()
    
    # Your exact example
    test_input = r"\text{Find the limit of each of the sequences } (a_n) \text{ in the following cases:} a_n = \frac{1}{2^n}"
    
    result = translator.translate_complete(test_input)
    
    print("\n" + "=" * 60)
    print("🎯 Expected Result:")
    print(r"\text{Tentukan limit dari setiap barisan } (a_n) \text{ dalam kasus berikut:} a_n = \frac{1}{2^n}")
    
    print("\n🎉 Workflow completed!")
    return result

if __name__ == "__main__":
    test_simplified_workflow()
